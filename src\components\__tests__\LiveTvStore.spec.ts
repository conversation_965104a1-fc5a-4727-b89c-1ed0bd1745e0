import { describe, it, expect, beforeEach } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useLiveTvStore } from '@/stores/counter'

describe('LiveTV Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('should initialize with empty state', () => {
    const store = useLiveTvStore()
    
    expect(store.events).toEqual({})
    expect(store.channels).toEqual([])
    expect(store.loading).toBe(false)
    expect(store.error).toBe(null)
    expect(store.selectedEvent).toBe(null)
    expect(store.selectedChannel).toBe(null)
  })

  it('should fetch events successfully', async () => {
    const store = useLiveTvStore()
    
    await store.fetchEvents()
    
    expect(store.loading).toBe(false)
    expect(store.error).toBe(null)
    expect(Object.keys(store.events).length).toBeGreaterThan(0)
  })

  it('should fetch channels successfully', async () => {
    const store = useLiveTvStore()
    
    await store.fetchChannels()
    
    expect(store.loading).toBe(false)
    expect(store.error).toBe(null)
    expect(store.channels.length).toBeGreaterThan(0)
  })

  it('should select event correctly', async () => {
    const store = useLiveTvStore()
    await store.fetchEvents()
    
    const firstEvent = Object.values(store.events)[0]?.[0]
    if (firstEvent) {
      store.selectEvent(firstEvent)
      
      expect(store.selectedEvent).toEqual(firstEvent)
      expect(store.selectedChannel).toBe(firstEvent.channels[0])
    }
  })

  it('should select channel correctly', () => {
    const store = useLiveTvStore()
    const testChannel = 'https://test-channel.com'
    
    store.selectChannel(testChannel)
    
    expect(store.selectedChannel).toBe(testChannel)
  })

  it('should clear selection correctly', async () => {
    const store = useLiveTvStore()
    await store.fetchEvents()
    
    const firstEvent = Object.values(store.events)[0]?.[0]
    if (firstEvent) {
      store.selectEvent(firstEvent)
      store.clearSelection()
      
      expect(store.selectedEvent).toBe(null)
      expect(store.selectedChannel).toBe(null)
    }
  })
})
