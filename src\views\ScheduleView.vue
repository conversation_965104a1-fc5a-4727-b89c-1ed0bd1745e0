<template>
  <div class="min-h-screen bg-black text-white">
    <!-- Hero Header -->
    <section class="relative py-16 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
      <!-- Background Elements -->
      <div class="absolute inset-0">
        <div class="absolute top-10 left-10 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div
          class="absolute bottom-10 right-10 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000">
        </div>
      </div>

      <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h1
            class="text-4xl md:text-6xl font-black mb-6 bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent">
            Event Schedule
          </h1>
          <p class="text-xl text-gray-300 max-w-2xl mx-auto">
            Never miss your favorite events with our comprehensive schedule
          </p>
        </div>

        <!-- Interactive Controls -->
        <div class="flex flex-col lg:flex-row gap-6 justify-center items-center">
          <!-- Sport Filter -->
          <div class="relative group">
            <select v-model="selectedSport"
              class="appearance-none px-6 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl text-white font-medium pr-12 focus:outline-none focus:border-purple-500/50 transition-all duration-300 hover:bg-white/15">
              <option value="" class="bg-slate-800 text-white">All Sports</option>
              <option v-for="sport in availableSports" :key="sport" :value="sport" class="bg-slate-800 text-white">
                {{ apiService.getSportIcon(sport) }} {{ sport }}
              </option>
            </select>
            <div class="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
              <Calendar class="w-5 h-5 text-gray-400" />
            </div>
          </div>

          <!-- Refresh Button -->
          <button @click="refreshEvents" :disabled="loading"
            class="group px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-bold rounded-2xl hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-purple-500/50 disabled:opacity-50 disabled:cursor-not-allowed">
            <RefreshCw :class="['w-5 h-5 mr-2 inline', { 'animate-spin': loading }]" />
            Refresh Events
          </button>
        </div>
      </div>
    </section>

    <!-- Timeline View -->
    <section class="py-16 relative">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Loading State -->
        <div v-if="loading" class="space-y-8">
          <div v-for="i in 3" :key="i" class="relative">
            <!-- Timeline Line -->
            <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-purple-500/50 to-transparent"></div>

            <!-- Loading Card -->
            <div class="ml-16 bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
              <div class="animate-pulse space-y-4">
                <div class="h-4 bg-white/10 rounded w-1/4"></div>
                <div class="h-6 bg-white/10 rounded w-3/4"></div>
                <div class="h-4 bg-white/10 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Events Timeline -->
        <div v-else class="relative">
          <!-- Timeline Line -->
          <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-purple-500 via-pink-500 to-blue-500">
          </div>

          <div v-for="(dayEvents, date) in filteredEvents" :key="date" class="mb-16">
            <!-- Date Header -->
            <div class="relative mb-8">
              <div
                class="absolute left-8 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full shadow-lg">
              </div>
              <div class="ml-16">
                <h2 class="text-2xl md:text-3xl font-bold text-white mb-2">
                  {{ formatDate(date) }}
                </h2>
                <p class="text-gray-400">{{ dayEvents.length }} event{{ dayEvents.length !== 1 ? 's' : '' }} scheduled
                </p>
              </div>
            </div>

            <!-- Events for this date -->
            <div class="space-y-6">
              <div v-for="event in dayEvents" :key="event.unix_timestamp"
                class="group relative ml-16 bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:border-purple-500/50 transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl hover:shadow-purple-500/20">

                <!-- Event Status Indicator -->
                <div
                  class="absolute -left-8 top-8 w-4 h-4 rounded-full border-2 border-white/20 group-hover:border-purple-500 transition-colors duration-300"
                  :class="{
                    'bg-red-500 animate-pulse': apiService.isEventLive(event.unix_timestamp),
                    'bg-green-500': event.isCustom,
                    'bg-blue-500': !apiService.isEventLive(event.unix_timestamp) && !event.isCustom
                  }">
                </div>

                <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
                  <!-- Event Content -->
                  <div class="flex-1">
                    <!-- Event Header -->
                    <div class="flex flex-wrap items-center gap-3 mb-4">
                      <span
                        class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-500/20 text-purple-300 border border-purple-500/30">
                        {{ apiService.getSportIcon(event.sport) }} {{ event.sport }}
                      </span>

                      <!-- Live Indicator -->
                      <span v-if="apiService.isEventLive(event.unix_timestamp)"
                        class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-500/20 text-red-300 border border-red-500/30 animate-pulse">
                        <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                        LIVE NOW
                      </span>

                      <!-- Custom Event Indicator -->
                      <span v-else-if="event.isCustom"
                        class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-500/20 text-green-300 border border-green-500/30">
                        <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        CUSTOM
                      </span>

                      <!-- Tournament -->
                      <span class="text-gray-400 text-sm">{{ event.tournament }}</span>
                    </div>

                    <!-- Event Title -->
                    <h3
                      class="text-xl md:text-2xl font-bold text-white mb-3 group-hover:text-purple-300 transition-colors duration-300">
                      {{ event.match }}
                    </h3>

                    <!-- Event Description -->
                    <p v-if="event.description" class="text-gray-400 mb-4 leading-relaxed">
                      {{ event.description }}
                    </p>

                    <!-- Event Details -->
                    <div class="flex flex-wrap items-center gap-6 text-sm text-gray-400">
                      <div class="flex items-center">
                        <Tv class="w-4 h-4 mr-2 text-purple-400" />
                        <span>{{ event.channels.length }} channel{{ event.channels.length !== 1 ? 's' : '' }}</span>
                      </div>
                      <div class="flex items-center">
                        <Clock class="w-4 h-4 mr-2 text-blue-400" />
                        <span>{{ apiService.getTimeUntilEvent(event.unix_timestamp) }}</span>
                      </div>
                      <div class="flex items-center">
                        <Calendar class="w-4 h-4 mr-2 text-pink-400" />
                        <span>{{ apiService.getEventTime(event.unix_timestamp) }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- Action Buttons -->
                  <div class="flex flex-col sm:flex-row gap-3 lg:flex-shrink-0">
                    <button @click.stop="setReminder(event)"
                      class="group/btn inline-flex items-center justify-center px-6 py-3 text-sm font-medium text-gray-300 hover:text-white bg-white/5 hover:bg-white/10 border border-white/20 hover:border-purple-500/50 rounded-xl transition-all duration-300 hover:scale-105">
                      <Bell class="w-4 h-4 mr-2 group-hover/btn:animate-bounce" />
                      Set Reminder
                    </button>

                    <button @click.stop="goToLive(event)"
                      class="group/btn inline-flex items-center justify-center px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 rounded-xl transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-purple-500/50">
                      <Play class="w-4 h-4 mr-2 group-hover/btn:scale-110 transition-transform duration-300" />
                      Watch Live
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="Object.keys(filteredEvents).length === 0" class="text-center py-20">
            <div class="relative">
              <div
                class="w-32 h-32 mx-auto mb-8 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full flex items-center justify-center">
                <Calendar class="w-16 h-16 text-gray-400" />
              </div>
              <div
                class="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm font-bold">?</span>
              </div>
            </div>
            <h3 class="text-2xl font-bold text-white mb-4">No events found</h3>
            <p class="text-gray-400 max-w-md mx-auto text-lg">
              {{ emptyStateMessage }}
            </p>
            <button @click="refreshEvents"
              class="mt-8 inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-xl hover:scale-105 transition-all duration-300">
              <RefreshCw class="w-4 h-4 mr-2" />
              Refresh Schedule
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Quick Stats -->
    <section class="py-16 bg-gradient-to-r from-purple-900/20 to-pink-900/20 relative">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="text-center">
            <div
              class="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center">
              <Calendar class="w-8 h-8 text-white" />
            </div>
            <div class="text-3xl font-bold text-white mb-2">{{ Object.keys(filteredEvents).length }}</div>
            <div class="text-gray-400">Days with Events</div>
          </div>

          <div class="text-center">
            <div
              class="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-2xl flex items-center justify-center">
              <Tv class="w-8 h-8 text-white" />
            </div>
            <div class="text-3xl font-bold text-white mb-2">{{ totalEvents }}</div>
            <div class="text-gray-400">Total Events</div>
          </div>

          <div class="text-center">
            <div
              class="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl flex items-center justify-center">
              <Clock class="w-8 h-8 text-white" />
            </div>
            <div class="text-3xl font-bold text-white mb-2">{{ liveEvents }}</div>
            <div class="text-gray-400">Live Now</div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useLiveTvStore } from '@/stores/counter'
import { apiService } from '@/services/api'
import {
  Calendar,
  Tv,
  Clock,
  Bell,
  Play,
  RefreshCw
} from 'lucide-vue-next'

const router = useRouter()
const store = useLiveTvStore()
const selectedSport = ref('')

const loading = computed(() => store.loading)
const events = computed(() => store.events)

const filteredEvents = computed(() => {
  if (!selectedSport.value) return store.eventsByDate
  return store.getEventsBySport(selectedSport.value)
})

const availableSports = computed(() => {
  return store.allSports
})

const totalEvents = computed(() => {
  return Object.values(filteredEvents.value).flat().length
})

const liveEvents = computed(() => {
  return Object.values(filteredEvents.value)
    .flat()
    .filter(event => apiService.isEventLive(event.unix_timestamp)).length
})

const emptyStateMessage = computed(() => {
  if (selectedSport.value) {
    return `No ${selectedSport.value.toLowerCase()} events scheduled for the selected period. Try refreshing or selecting a different sport.`
  }
  return 'No events scheduled for the selected period. Check back later or try refreshing the schedule.'
})

const formatDate = (dateString: string | number) => {
  const date = new Date(String(dateString))
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)

  if (date.toDateString() === today.toDateString()) {
    return 'Today'
  } else if (date.toDateString() === tomorrow.toDateString()) {
    return 'Tomorrow'
  } else {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }
}

const refreshEvents = async () => {
  await store.fetchEvents()
}

const setReminder = (event: any) => {
  // Enhanced reminder functionality
  const notification = new Notification('Event Reminder Set', {
    body: `You'll be notified 15 minutes before "${event.match}" starts`,
    icon: '/favicon.ico'
  })

  // Store reminder in localStorage
  const reminders = JSON.parse(localStorage.getItem('eventReminders') || '[]')
  reminders.push({
    eventId: event.unix_timestamp,
    match: event.match,
    time: event.unix_timestamp,
    createdAt: Date.now()
  })
  localStorage.setItem('eventReminders', JSON.stringify(reminders))
}

const goToLive = (event: any) => {
  store.selectEvent(event)
  router.push('/live')
}

onMounted(async () => {
  await store.fetchEvents()
  store.loadCustomEvents()

  // Request notification permission
  if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission()
  }
})
</script>

<style scoped>
/* Custom scrollbar for the select dropdown */
select::-ms-expand {
  display: none;
}

select {
  background-image: none;
}

/* Smooth animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.group {
  animation: slideIn 0.6s ease-out;
}

/* Timeline animation */
.group:nth-child(1) {
  animation-delay: 0.1s;
}

.group:nth-child(2) {
  animation-delay: 0.2s;
}

.group:nth-child(3) {
  animation-delay: 0.3s;
}

.group:nth-child(4) {
  animation-delay: 0.4s;
}

.group:nth-child(5) {
  animation-delay: 0.5s;
}
</style>
