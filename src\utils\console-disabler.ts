// Console disabler to hide all logs from users
// This prevents users from seeing API responses and sensitive data

class ConsoleDisabler {
  private originalConsole: any = {}
  private isDisabled = false

  constructor() {
    // Store original console methods
    this.originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info,
      debug: console.debug,
      trace: console.trace,
      table: console.table,
      group: console.group,
      groupEnd: console.groupEnd,
      time: console.time,
      timeEnd: console.timeEnd,
      clear: console.clear
    }
  }

  // Disable all console methods
  disable() {
    if (this.isDisabled) return

    // Replace all console methods with empty functions
    console.log = () => {}
    console.error = () => {}
    console.warn = () => {}
    console.info = () => {}
    console.debug = () => {}
    console.trace = () => {}
    console.table = () => {}
    console.group = () => {}
    console.groupEnd = () => {}
    console.time = () => {}
    console.timeEnd = () => {}
    console.clear = () => {}

    // Also disable console.dir and other methods
    if (console.dir) console.dir = () => {}
    if (console.dirxml) console.dirxml = () => {}
    if (console.count) console.count = () => {}
    if (console.countReset) console.countReset = () => {}
    if (console.assert) console.assert = () => {}

    this.isDisabled = true
  }

  // Enable console (for development)
  enable() {
    if (!this.isDisabled) return

    // Restore original console methods
    console.log = this.originalConsole.log
    console.error = this.originalConsole.error
    console.warn = this.originalConsole.warn
    console.info = this.originalConsole.info
    console.debug = this.originalConsole.debug
    console.trace = this.originalConsole.trace
    console.table = this.originalConsole.table
    console.group = this.originalConsole.group
    console.groupEnd = this.originalConsole.groupEnd
    console.time = this.originalConsole.time
    console.timeEnd = this.originalConsole.timeEnd
    console.clear = this.originalConsole.clear

    this.isDisabled = false
  }

  // Check if console is disabled
  isConsoleDisabled(): boolean {
    return this.isDisabled
  }

  // Conditional disable based on environment
  disableInProduction() {
    // Disable console in production or when not in development
    if (import.meta.env.PROD || import.meta.env.MODE === 'production') {
      this.disable()
    }
  }

  // Force disable regardless of environment
  forceDisable() {
    this.disable()
  }

  // Allow specific console methods (for debugging)
  allowOnly(methods: string[] = []) {
    this.disable()
    
    methods.forEach(method => {
      if (this.originalConsole[method]) {
        (console as any)[method] = this.originalConsole[method]
      }
    })
  }
}

// Create singleton instance
export const consoleDisabler = new ConsoleDisabler()

// Auto-disable in production
consoleDisabler.disableInProduction()

// Export for manual control
export default consoleDisabler
