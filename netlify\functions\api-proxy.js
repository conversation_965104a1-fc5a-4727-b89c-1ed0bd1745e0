// Netlify Function to proxy API calls and implement caching
// This hides the real API from users and provides smart caching

const REAL_API_URL = 'https://topembed.pw/api.php';
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

// In-memory cache (for serverless, you might want to use Redis in production)
let cache = {
  data: null,
  timestamp: null
};

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json',
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  // Only allow GET requests
  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' }),
    };
  }

  try {
    const now = Date.now();
    
    // Check if we have valid cached data
    if (cache.data && cache.timestamp && (now - cache.timestamp) < CACHE_DURATION) {
      console.log('Serving from cache');
      return {
        statusCode: 200,
        headers: {
          ...headers,
          'X-Cache': 'HIT',
          'Cache-Control': 'public, max-age=300', // 5 minutes
        },
        body: JSON.stringify({
          success: true,
          data: cache.data,
          cached: true,
          timestamp: cache.timestamp
        }),
      };
    }

    // Fetch fresh data from real API
    console.log('Fetching fresh data from API');
    const response = await fetch(REAL_API_URL);
    
    if (!response.ok) {
      throw new Error(`API responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    // Update cache
    cache = {
      data: data,
      timestamp: now
    };

    return {
      statusCode: 200,
      headers: {
        ...headers,
        'X-Cache': 'MISS',
        'Cache-Control': 'public, max-age=300', // 5 minutes
      },
      body: JSON.stringify({
        success: true,
        data: data,
        cached: false,
        timestamp: now
      }),
    };

  } catch (error) {
    console.error('API Proxy Error:', error);
    
    // If we have cached data, serve it even if it's expired
    if (cache.data) {
      console.log('Serving stale cache due to error');
      return {
        statusCode: 200,
        headers: {
          ...headers,
          'X-Cache': 'STALE',
        },
        body: JSON.stringify({
          success: true,
          data: cache.data,
          cached: true,
          stale: true,
          timestamp: cache.timestamp
        }),
      };
    }

    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Failed to fetch events data',
        message: 'Service temporarily unavailable'
      }),
    };
  }
};
