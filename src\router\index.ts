import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import { authService } from '@/services/auth'
import IPTV from '../views/IPTV.vue'


const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/live',
      name: 'live',
      component: () => import('../views/LiveView.vue'),
    },
    {
      path: '/schedule',
      name: 'schedule',
      component: () => import('../views/ScheduleView.vue'),
    },
    {
      path: '/secret-admin-portal-2024',
      name: 'secret-admin-login',
      component: () => import('../views/AdminLoginView.vue'),
    },
    {
      path: '/iptv',
      name: 'iptv',
      component: IPTV,
    },
    {
      path: '/admin',
      name: 'admin',
      component: () => import('../views/AdminView.vue'),
      beforeEnter: (to, from, next) => {
        // Check if user is authenticated using secure auth service
        if (authService.isAuthenticated()) {
          next()
        } else {
          next('/secret-admin-portal-2024')
        }
      }
    },
    // Legacy redirect for old admin URLs
    {
      path: '/admin/login',
      redirect: '/secret-admin-portal-2024'
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
    },
  ],
})

export default router
