@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import './base.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.live-indicator {
  animation: pulse 2s infinite;
}

.live-indicator::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 2s infinite;
}

/* Cricket-themed animations and effects */
.cricket-hero-bg {
  background-attachment: fixed;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

@keyframes cricket-ball-bounce {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.cricket-ball-animation {
  animation: cricket-ball-bounce 3s ease-in-out infinite;
}

.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: shimmer 3s infinite;
}

/* Modern Typewriter/Dashed Text Effects */
@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blinkCursor {
  from, to { border-color: transparent; }
  50% { border-color: #ff6b35; }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    transform: translateY(30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes textGlow {
  0%, 100% {
    text-shadow: 0 0 20px rgba(255, 107, 53, 0.3), 0 0 40px rgba(255, 107, 53, 0.1);
  }
  50% {
    text-shadow: 0 0 30px rgba(255, 107, 53, 0.6), 0 0 60px rgba(255, 107, 53, 0.3);
  }
}

@keyframes letterSpacing {
  0% { letter-spacing: -0.1em; opacity: 0; }
  50% { letter-spacing: 0.1em; }
  100% { letter-spacing: 0; opacity: 1; }
}

/* Modern Text Classes */
.typewriter-text {
  overflow: hidden;
  border-right: 4px solid #ff6b35;
  white-space: nowrap;
  animation: typewriter 3s steps(15, end), blinkCursor 1s infinite;
  animation-delay: 0.8s;
  animation-fill-mode: both;
  position: relative;
}

.typewriter-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 107, 53, 0.1) 50%, transparent 100%);
  animation: typewriterGlow 3s ease-in-out;
  animation-delay: 0.8s;
  animation-fill-mode: both;
}

@keyframes typewriterGlow {
  0% { opacity: 0; transform: translateX(-100%); }
  50% { opacity: 1; transform: translateX(0%); }
  100% { opacity: 0; transform: translateX(100%); }
}

.slide-in-left {
  animation: slideInFromLeft 1s ease-out;
  animation-fill-mode: both;
}

.slide-in-right {
  animation: slideInFromRight 1s ease-out;
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

.fade-in-up {
  animation: fadeInUp 1s ease-out;
  animation-delay: 0.6s;
  animation-fill-mode: both;
}

.modern-text-glow {
  animation: textGlow 3s ease-in-out infinite;
}

.letter-spacing-effect {
  animation: letterSpacing 1.5s ease-out;
  animation-fill-mode: both;
}

/* Modern Typography */
.hero-title-modern {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 900;
  letter-spacing: -0.02em;
  line-height: 0.9;
  background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
}

.hero-subtitle-modern {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 800;
  letter-spacing: -0.01em;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 50%, #ff4757 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.hero-subtitle-modern::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #ff6b35, #f7931e, #ff4757);
  border-radius: 2px;
  animation: slideInFromLeft 1s ease-out;
  animation-delay: 1.5s;
  animation-fill-mode: both;
  opacity: 0;
  animation-name: slideInFromLeft, fadeInUp;
}

.hero-tagline-modern {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 600;
  letter-spacing: 0.05em;
  color: #e2e8f0;
  text-transform: uppercase;
  font-size: 0.9em;
}

/* Enhanced text shadows for better readability */
.hero-text-shadow {
  text-shadow: 2px 2px 4px rgba(0,0,0,0.8), 0 0 20px rgba(0,0,0,0.5);
}

/* Subtle glowing button effect */
.glow-button {
  box-shadow: 0 0 15px rgba(255, 165, 0, 0.2);
  transition: all 0.3s ease;
}

.glow-button:hover {
  box-shadow: 0 0 20px rgba(255, 165, 0, 0.4), 0 0 25px rgba(255, 165, 0, 0.2);
}

/* Additional Cricket-themed Animations */
@keyframes scoreboardFlip {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(90deg); }
  100% { transform: rotateY(0deg); }
}

@keyframes wicketFall {
  0% { transform: translateY(0) rotate(0deg); opacity: 1; }
  50% { transform: translateY(-10px) rotate(180deg); opacity: 0.7; }
  100% { transform: translateY(20px) rotate(360deg); opacity: 0; }
}

@keyframes boundaryRun {
  0% { transform: translateX(-100px) scale(0.8); opacity: 0; }
  50% { transform: translateX(0) scale(1.1); opacity: 1; }
  100% { transform: translateX(100px) scale(0.8); opacity: 0; }
}

@keyframes stadiumWave {
  0%, 100% { transform: translateY(0) scale(1); }
  25% { transform: translateY(-5px) scale(1.05); }
  50% { transform: translateY(-10px) scale(1.1); }
  75% { transform: translateY(-5px) scale(1.05); }
}

@keyframes cricketBallSpin {
  0% { transform: rotate(0deg) translateX(0); }
  25% { transform: rotate(90deg) translateX(5px); }
  50% { transform: rotate(180deg) translateX(0); }
  75% { transform: rotate(270deg) translateX(-5px); }
  100% { transform: rotate(360deg) translateX(0); }
}

/* Utility classes for cricket animations */
.scoreboard-flip {
  animation: scoreboardFlip 2s ease-in-out infinite;
}

.wicket-fall {
  animation: wicketFall 1.5s ease-out;
}

.boundary-run {
  animation: boundaryRun 3s ease-in-out infinite;
}

.stadium-wave {
  animation: stadiumWave 4s ease-in-out infinite;
}

.cricket-ball-spin {
  animation: cricketBallSpin 2s linear infinite;
}

/* Enhanced gradient backgrounds */
.cricket-gradient-1 {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 50%, #ff6b35 100%);
}

.cricket-gradient-2 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
}

.cricket-gradient-3 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #f093fb 100%);
}

/* Interactive hover effects */
.cricket-card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cricket-card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Text effects */
.cricket-text-glow {
  text-shadow: 0 0 10px rgba(255, 165, 0, 0.5), 0 0 20px rgba(255, 165, 0, 0.3);
}

.cricket-text-shadow-heavy {
  text-shadow: 2px 2px 4px rgba(0,0,0,0.8), 0 0 20px rgba(0,0,0,0.5), 0 0 40px rgba(255,165,0,0.3);
}

/* Loading animations */
@keyframes cricketLoading {
  0% { transform: rotate(0deg) scale(1); }
  25% { transform: rotate(90deg) scale(1.1); }
  50% { transform: rotate(180deg) scale(1); }
  75% { transform: rotate(270deg) scale(0.9); }
  100% { transform: rotate(360deg) scale(1); }
}

.cricket-loading {
  animation: cricketLoading 1.5s ease-in-out infinite;
}

/* Accessibility - Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-pulse,
  .animate-bounce,
  .animate-spin,
  .animate-float,
  .animate-glow,
  .animate-cricket-spin {
    animation: none !important;
  }
}

/* User-friendly animation timing */
.animate-fade-in {
  animation-duration: 0.8s;
  animation-timing-function: ease-out;
}

.animate-slide-up {
  animation-duration: 0.6s;
  animation-timing-function: ease-out;
}

/* Subtle hover effects */
.subtle-hover {
  transition: all 0.2s ease-out;
}

.subtle-hover:hover {
  transform: translateY(-2px);
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .hero-text-shadow {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8), 0 0 10px rgba(0,0,0,0.5);
  }

  .cricket-text-shadow-heavy {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8), 0 0 10px rgba(0,0,0,0.5);
  }

  /* Reduce animations on mobile for better performance */
  .animate-float,
  .animate-glow,
  .animate-cricket-spin {
    animation: none;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Dropdown styling for better visibility */
select {
  background-color: white !important;
  color: #1f2937 !important;
}

select option {
  background-color: white !important;
  color: #1f2937 !important;
  font-weight: 500 !important;
  padding: 8px 12px !important;
}

select option:hover {
  background-color: #f3f4f6 !important;
  color: #1f2937 !important;
}

select option:checked {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* Input field styling for better visibility */
input[type="text"], input[type="search"], input[type="url"], input[type="date"], input[type="time"], textarea {
  background-color: white !important;
  color: #1f2937 !important;
  font-weight: 500 !important;
}

input[type="text"]::placeholder, input[type="search"]::placeholder, input[type="url"]::placeholder, textarea::placeholder {
  color: #6b7280 !important;
  font-weight: 400 !important;
}

input[type="text"]:focus, input[type="search"]:focus, input[type="url"]:focus, input[type="date"]:focus, input[type="time"]:focus, textarea:focus {
  background-color: white !important;
  color: #1f2937 !important;
  outline: none !important;
}

/* Ensure form inputs are always visible */
.modal input, .modal textarea, .modal select {
  background-color: white !important;
  color: #1f2937 !important;
  border: 1px solid #d1d5db !important;
}

.modal input:focus, .modal textarea:focus, .modal select:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Responsive improvements for Schedule page */
@media (max-width: 640px) {
  .schedule-card {
    margin: 0 -1rem;
    border-radius: 0;
  }

  .schedule-header {
    padding: 1rem;
  }

  .event-tags {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .event-title {
    font-size: 1rem;
    line-height: 1.4;
  }

  .action-buttons {
    width: 100%;
  }

  .action-buttons button {
    flex: 1;
    min-width: 0;
  }
}

/* Better touch targets for mobile */
@media (max-width: 768px) {
  .event-card {
    min-height: 44px;
  }

  .event-card button {
    min-height: 44px;
    padding: 0.75rem 1rem;
  }

  .sport-filter select {
    min-height: 44px;
  }
}
