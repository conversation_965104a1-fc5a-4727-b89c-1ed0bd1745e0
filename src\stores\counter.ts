import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { apiService, type LiveEvent, type Channel } from '@/services/api'
import { databaseService } from '@/services/database'

export const useLiveTvStore = defineStore('livetv', () => {
  const events = ref<{ [date: string]: LiveEvent[] }>({})
  const channels = ref<Channel[]>([])
  const customEvents = ref<{ [date: string]: LiveEvent[] }>({})
  const loading = ref(false)
  const error = ref<string | null>(null)
  const selectedEvent = ref<LiveEvent | null>(null)
  const selectedChannel = ref<string | null>(null)

  const todayEvents = computed(() => {
    const today = new Date().toISOString().split('T')[0]
    return events.value[today] || []
  })

  const liveEvents = computed(() => {
    return apiService.filterLiveEvents(events.value)
  })

  const upcomingEvents = computed(() => {
    return apiService.getUpcomingEvents(events.value, 5)
  })

  const allSports = computed(() => {
    return apiService.getAllSports(events.value)
  })

  const liveChannels = computed(() => {
    return channels.value.filter(channel => channel.isLive)
  })

  const totalEvents = computed(() => {
    return Object.values(events.value).flat().length
  })

  const allEvents = computed(() => {
    // Merge API events with custom events
    const merged: { [date: string]: LiveEvent[] } = {}

    // Add API events
    Object.entries(events.value).forEach(([date, dayEvents]) => {
      merged[date] = [...dayEvents]
    })

    // Add custom events (they take priority)
    Object.entries(customEvents.value).forEach(([date, dayEvents]) => {
      if (merged[date]) {
        merged[date] = [...merged[date], ...dayEvents]
      } else {
        merged[date] = [...dayEvents]
      }
    })

    return merged
  })

  const eventsByDate = computed(() => {
    // Sort dates and return organized events (including custom events)
    const sortedDates = Object.keys(allEvents.value).sort()
    const organized: { [date: string]: LiveEvent[] } = {}

    sortedDates.forEach(date => {
      organized[date] = allEvents.value[date].sort((a, b) => a.unix_timestamp - b.unix_timestamp)
    })

    return organized
  })

  async function fetchEvents(date?: string) {
    loading.value = true
    error.value = null

    try {
      const response = await apiService.getLiveEvents(date)
      events.value = response.events
    } catch (err) {
      error.value = 'Failed to fetch live events'
    } finally {
      loading.value = false
    }
  }

  async function fetchChannels() {
    loading.value = true
    error.value = null

    try {
      const response = await apiService.getChannels()
      channels.value = response
    } catch (err) {
      error.value = 'Failed to fetch channels'
      console.error('Error fetching channels:', err)
    } finally {
      loading.value = false
    }
  }

  function selectEvent(event: LiveEvent) {
    selectedEvent.value = event
    if (event.channels.length > 0) {
      selectedChannel.value = event.channels[0]
    }
  }

  function selectChannel(channelUrl: string) {
    selectedChannel.value = channelUrl
  }

  function clearSelection() {
    selectedEvent.value = null
    selectedChannel.value = null
  }

  function getEventsBySport(sport: string) {
    const filtered: { [date: string]: LiveEvent[] } = {}

    Object.entries(events.value).forEach(([date, dayEvents]) => {
      const sportEvents = dayEvents.filter(event => event.sport === sport)
      if (sportEvents.length > 0) {
        filtered[date] = sportEvents
      }
    })

    return filtered
  }

  function getEventsForDate(date: string) {
    return apiService.getEventsByDate(events.value, date)
  }

  function refreshData() {
    return Promise.all([fetchEvents(), fetchChannels()])
  }

  async function addCustomEvent(eventData: {
    match: string
    sport: string
    tournament: string
    date: string
    time: string
    iframeHtml: string
    description?: string
  }) {
    try {
      // Add to Supabase database
      const newEvent = await databaseService.addCustomEvent(eventData)

      // Update local state
      const dateKey = eventData.date
      if (customEvents.value[dateKey]) {
        customEvents.value[dateKey].push(newEvent)
      } else {
        customEvents.value[dateKey] = [newEvent]
      }

      return newEvent
    } catch (error) {
      console.error('Error adding custom event:', error)
      throw error
    }
  }

  async function removeCustomEvent(eventId: string) {
    try {
      // Remove from Supabase database
      await databaseService.removeCustomEvent(eventId)

      // Update local state
      Object.keys(customEvents.value).forEach(date => {
        customEvents.value[date] = customEvents.value[date].filter(event =>
          !(event as any).id || (event as any).id !== eventId
        )
        if (customEvents.value[date].length === 0) {
          delete customEvents.value[date]
        }
      })
    } catch (error) {
      console.error('Error removing custom event:', error)
      throw error
    }
  }

  async function loadCustomEvents() {
    try {
      // Load from Supabase database
      const events = await databaseService.getCustomEvents()

      // Organize by date
      customEvents.value = {}
      events.forEach(event => {
        const date = new Date(event.unix_timestamp * 1000).toISOString().split('T')[0]
        if (customEvents.value[date]) {
          customEvents.value[date].push(event)
        } else {
          customEvents.value[date] = [event]
        }
      })
    } catch (error) {
      // Fallback to localStorage if Supabase fails
      try {
        const saved = localStorage.getItem('customEvents')
        if (saved) {
          customEvents.value = JSON.parse(saved)
        }
      } catch (localError) {
        // Silent fallback
      }
    }
  }

  function getCustomEvents() {
    return customEvents.value
  }

  // Real-time subscription
  let customEventsSubscription: any = null

  function subscribeToCustomEvents() {
    if (customEventsSubscription) return

    customEventsSubscription = databaseService.subscribeToCustomEvents(async (events) => {
      // Update local state when database changes
      customEvents.value = {}
      events.forEach(event => {
        const date = new Date(event.unix_timestamp * 1000).toISOString().split('T')[0]
        if (customEvents.value[date]) {
          customEvents.value[date].push(event)
        } else {
          customEvents.value[date] = [event]
        }
      })
      // Real-time update completed
    })
  }

  function unsubscribeFromCustomEvents() {
    if (customEventsSubscription) {
      databaseService.unsubscribeFromCustomEvents(customEventsSubscription)
      customEventsSubscription = null
    }
  }

  return {
    // State
    events,
    channels,
    customEvents,
    loading,
    error,
    selectedEvent,
    selectedChannel,

    // Computed
    todayEvents,
    liveEvents,
    upcomingEvents,
    allSports,
    liveChannels,
    totalEvents,
    allEvents,
    eventsByDate,

    // Actions
    fetchEvents,
    fetchChannels,
    selectEvent,
    selectChannel,
    clearSelection,
    getEventsBySport,
    getEventsForDate,
    refreshData,
    addCustomEvent,
    removeCustomEvent,
    loadCustomEvents,
    getCustomEvents,
    subscribeToCustomEvents,
    unsubscribeFromCustomEvents
  }
})
