<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useLiveTvStore } from '@/stores/counter'
import { Play, Users, Tv, Calendar, Star, ArrowRight, Globe, Zap, Shield, Smartphone } from 'lucide-vue-next'

const store = useLiveTvStore()
const stats = ref({
  liveEvents: 0,
  totalChannels: 0,
  activeUsers: 1247,
  totalEvents: 0
})

onMounted(async () => {
  await store.fetchEvents()
  await store.fetchChannels()

  stats.value.liveEvents = store.liveEvents.length
  stats.value.totalChannels = store.channels.length
  stats.value.totalEvents = store.totalEvents

  // Update stats every 30 seconds
  setInterval(() => {
    stats.value.liveEvents = store.liveEvents.length
    stats.value.activeUsers = Math.floor(Math.random() * 2000) + 800
  }, 30000)
})
</script>

<template>
  <div class="bg-black text-white min-h-screen">
    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center overflow-hidden">
      <!-- Background Video -->
      <div class="absolute inset-0 z-0">
        <video autoplay loop muted playsinline class="w-full h-full object-cover">
          <source src="/backgroundVideoHome.mp4" type="video/mp4">
          Your browser does not support the video tag.
        </video>
        <div class="absolute inset-0 bg-black/60 backdrop-blur-sm"></div>
        <div class="absolute inset-0 bg-gradient-to-br from-purple-900/50 via-transparent to-blue-900/30"></div>
      </div>

      <div class="relative z-10 text-center px-4 max-w-6xl mx-auto">
        <h1
          class="text-5xl md:text-7xl lg:text-8xl font-black mb-6 bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent drop-shadow-lg">
          GRAVITY
          <span class="block text-4xl md:text-6xl lg:text-7xl mt-2">LIVE TV</span>
        </h1>
        <p class="text-xl md:text-2xl text-gray-200 font-light max-w-3xl mx-auto leading-relaxed mb-12 drop-shadow-md">
          Experience premium entertainment with crystal-clear streaming,
          <span class="text-purple-300 font-medium">anywhere, anytime</span>
        </p>

        <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
          <router-link to="/live"
            class="group relative px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-bold rounded-full overflow-hidden hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-purple-500/50">
            <div class="relative flex items-center">
              <Play class="w-5 h-5 mr-2" />
              <span>Start Watching</span>
            </div>
          </router-link>

          <router-link to="/schedule"
            class="px-8 py-4 border-2 border-white/20 text-white font-bold rounded-full hover:bg-white/10 hover:border-white/40 transition-all duration-300">
            <span>View Schedule</span>
          </router-link>
        </div>

        <div class="flex items-center justify-center space-x-4 text-sm text-gray-400">
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span>LIVE NOW</span>
          </div>
          <span>•</span>
          <span>{{ stats.activeUsers.toLocaleString() }} watching</span>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="py-20 relative">
      <div class="max-w-7xl mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div class="text-center group">
            <div
              class="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Tv class="w-8 h-8 text-white" />
            </div>
            <div class="text-3xl md:text-4xl font-bold text-white mb-2">{{ stats.liveEvents }}</div>
            <div class="text-gray-400 text-sm">Live Channels</div>
          </div>

          <div class="text-center group">
            <div
              class="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Users class="w-8 h-8 text-white" />
            </div>
            <div class="text-3xl md:text-4xl font-bold text-white mb-2">{{ stats.activeUsers.toLocaleString() }}</div>
            <div class="text-gray-400 text-sm">Active Users</div>
          </div>

          <div class="text-center group">
            <div
              class="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Calendar class="w-8 h-8 text-white" />
            </div>
            <div class="text-3xl md:text-4xl font-bold text-white mb-2">{{ stats.totalEvents }}</div>
            <div class="text-gray-400 text-sm">Total Events</div>
          </div>

          <div class="text-center group">
            <div
              class="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-orange-600 to-red-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Globe class="w-8 h-8 text-white" />
            </div>
            <div class="text-3xl md:text-4xl font-bold text-white mb-2">{{ stats.totalChannels }}+</div>
            <div class="text-gray-400 text-sm">Countries</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-gradient-to-b from-black to-slate-900 relative">
      <div class="max-w-7xl mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-6xl font-bold text-white mb-6">
            Why Choose
            <span class="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">LiveTV</span>
          </h2>
          <p class="text-xl text-gray-400 max-w-2xl mx-auto">
            Experience the future of streaming with cutting-edge technology
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div
            class="group p-8 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 hover:border-purple-500/50 transition-all duration-300 hover:scale-105">
            <div
              class="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <Zap class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-white mb-4">Lightning Fast</h3>
            <p class="text-gray-400 leading-relaxed">
              Ultra-low latency streaming with adaptive bitrate technology
            </p>
          </div>

          <div
            class="group p-8 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 hover:border-blue-500/50 transition-all duration-300 hover:scale-105">
            <div
              class="w-16 h-16 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <Shield class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-white mb-4">Secure & Private</h3>
            <p class="text-gray-400 leading-relaxed">
              Military-grade encryption ensures your data remains private
            </p>
          </div>

          <div
            class="group p-8 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 hover:border-green-500/50 transition-all duration-300 hover:scale-105">
            <div
              class="w-16 h-16 bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
              <Smartphone class="w-8 h-8 text-white" />
            </div>
            <h3 class="text-2xl font-bold text-white mb-4">Multi-Device</h3>
            <p class="text-gray-400 leading-relaxed">
              Watch on any device with seamless synchronization
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Live Preview Section -->
    <section class="py-20 relative">
      <div class="max-w-7xl mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-6xl font-bold text-white mb-6">
            <span class="bg-gradient-to-r from-red-400 to-pink-400 bg-clip-text text-transparent">Live</span> Now
          </h2>
          <p class="text-xl text-gray-400 max-w-2xl mx-auto">
            Don't miss out on the latest content streaming right now
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div
            class="group relative bg-gradient-to-br from-purple-900/50 to-pink-900/50 backdrop-blur-sm rounded-2xl p-8 border border-purple-500/30 hover:border-purple-500/60 transition-all duration-300 hover:scale-105">
            <div class="absolute top-4 right-4">
              <div
                class="flex items-center space-x-2 bg-red-500 px-3 py-1 rounded-full text-white text-sm font-bold animate-pulse">
                <div class="w-2 h-2 bg-white rounded-full"></div>
                <span>LIVE</span>
              </div>
            </div>
            <div class="text-center">
              <div
                class="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center">
                <Tv class="w-10 h-10 text-white" />
              </div>
              <h3 class="text-2xl font-bold text-white mb-4">Premium Sports</h3>
              <p class="text-gray-300 mb-6">Live coverage of major sporting events</p>
              <router-link to="/live"
                class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-bold rounded-lg hover:scale-105 transition-all duration-300">
                <Play class="w-4 h-4 mr-2" />
                Watch Now
              </router-link>
            </div>
          </div>

          <div
            class="group relative bg-gradient-to-br from-blue-900/50 to-cyan-900/50 backdrop-blur-sm rounded-2xl p-8 border border-blue-500/30 hover:border-blue-500/60 transition-all duration-300 hover:scale-105">
            <div class="absolute top-4 right-4">
              <div
                class="flex items-center space-x-2 bg-red-500 px-3 py-1 rounded-full text-white text-sm font-bold animate-pulse">
                <div class="w-2 h-2 bg-white rounded-full"></div>
                <span>LIVE</span>
              </div>
            </div>
            <div class="text-center">
              <div
                class="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-2xl flex items-center justify-center">
                <Globe class="w-10 h-10 text-white" />
              </div>
              <h3 class="text-2xl font-bold text-white mb-4">Global News</h3>
              <p class="text-gray-300 mb-6">24/7 international news coverage</p>
              <router-link to="/live"
                class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-bold rounded-lg hover:scale-105 transition-all duration-300">
                <Play class="w-4 h-4 mr-2" />
                Watch Now
              </router-link>
            </div>
          </div>

          <div
            class="group relative bg-gradient-to-br from-green-900/50 to-emerald-900/50 backdrop-blur-sm rounded-2xl p-8 border border-green-500/30 hover:border-green-500/60 transition-all duration-300 hover:scale-105">
            <div class="absolute top-4 right-4">
              <div
                class="flex items-center space-x-2 bg-red-500 px-3 py-1 rounded-full text-white text-sm font-bold animate-pulse">
                <div class="w-2 h-2 bg-white rounded-full"></div>
                <span>LIVE</span>
              </div>
            </div>
            <div class="text-center">
              <div
                class="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl flex items-center justify-center">
                <Star class="w-10 h-10 text-white" />
              </div>
              <h3 class="text-2xl font-bold text-white mb-4">Entertainment</h3>
              <p class="text-gray-300 mb-6">Latest movies, shows, and events</p>
              <router-link to="/live"
                class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-bold rounded-lg hover:scale-105 transition-all duration-300">
                <Play class="w-4 h-4 mr-2" />
                Watch Now
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-purple-900/50 to-pink-900/50 relative">
      <div class="max-w-4xl mx-auto text-center px-4">
        <h2 class="text-4xl md:text-6xl font-bold text-white mb-6">
          Ready to Start Streaming?
        </h2>
        <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
          Join millions of users worldwide and experience the future of live streaming
        </p>
        <router-link to="/live"
          class="inline-flex items-center px-10 py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-bold text-lg rounded-full hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-purple-500/50">
          <Play class="w-6 h-6 mr-3" />
          Get Started Now
        </router-link>
      </div>
    </section>
  </div>
</template>

<style scoped>
@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
