<template>
  <!-- Modern Cricket-themed Footer -->
  <footer class="bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white relative overflow-hidden">
    <!-- Background Cricket Elements -->
    <div class="absolute inset-0 opacity-5">
      <div class="absolute top-10 left-10 text-4xl animate-float">🏏</div>
      <div class="absolute top-20 right-20 text-3xl animate-pulse-slow">🏆</div>
      <div class="absolute bottom-20 left-20 text-5xl animate-cricket-spin">⚡</div>
      <div class="absolute bottom-10 right-10 text-2xl animate-bounce-slow">🎯</div>
      <div class="absolute top-1/2 left-1/4 w-32 h-32 border-2 border-orange-400/20 rounded-full animate-pulse-slow">
      </div>
      <div class="absolute top-1/3 right-1/3 w-24 h-24 border-2 border-red-400/20 rounded-full animate-float"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <!-- Top Section with Newsletter -->
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold mb-4 hero-text-shadow">
          Stay Updated with
          <span class="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-red-400">Cricket News</span>
        </h2>
        <p class="text-gray-300 mb-8 max-w-2xl mx-auto">
          Get the latest match updates, scores, and exclusive cricket content delivered to your inbox
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto">
          <input type="email" placeholder="Enter your email"
            class="flex-1 px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" />
          <button
            class="px-8 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white font-bold rounded-lg hover:from-orange-600 hover:to-red-600 transition-all duration-300 transform hover:scale-105 shadow-lg">
            Subscribe 🏏
          </button>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
        <!-- Enhanced Brand Section -->
        <div class="lg:col-span-2">
          <div class="flex items-center space-x-3 mb-6">
            <div
              class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg animate-glow">
              <Tv class="w-7 h-7 text-white" />
            </div>
            <div>
              <span class="text-2xl font-bold">🏏 CricketLive</span>
              <div class="text-sm text-orange-400">Premium Sports Streaming</div>
            </div>
          </div>
          <p class="text-gray-300 mb-6 max-w-lg leading-relaxed">
            Experience the ultimate cricket streaming platform with HD quality, live commentary, and comprehensive
            coverage of all major tournaments. Join millions of cricket fans worldwide!
          </p>

          <!-- Enhanced Social Links -->
          <div class="flex space-x-4 mb-8">
            <a href="#"
              class="group w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg flex items-center justify-center hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-110 shadow-lg">
              <Facebook class="w-5 h-5 text-white group-hover:animate-pulse" />
            </a>
            <a href="#"
              class="group w-12 h-12 bg-gradient-to-r from-sky-500 to-sky-600 rounded-lg flex items-center justify-center hover:from-sky-600 hover:to-sky-700 transition-all duration-300 transform hover:scale-110 shadow-lg">
              <Twitter class="w-5 h-5 text-white group-hover:animate-pulse" />
            </a>
            <a href="#"
              class="group w-12 h-12 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg flex items-center justify-center hover:from-pink-600 hover:to-pink-700 transition-all duration-300 transform hover:scale-110 shadow-lg">
              <Instagram class="w-5 h-5 text-white group-hover:animate-pulse" />
            </a>
            <a href="#"
              class="group w-12 h-12 bg-gradient-to-r from-red-600 to-red-700 rounded-lg flex items-center justify-center hover:from-red-700 hover:to-red-800 transition-all duration-300 transform hover:scale-110 shadow-lg">
              <Youtube class="w-5 h-5 text-white group-hover:animate-pulse" />
            </a>
          </div>

          <!-- Live Stats -->
          <div class="grid grid-cols-2 gap-4">
            <div class="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
              <div class="flex items-center space-x-2 mb-2">
                <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span class="text-green-400 font-semibold">Live Now</span>
              </div>
              <div class="text-2xl font-bold text-white">{{ onlineUsers.toLocaleString() }}</div>
              <div class="text-gray-400 text-sm">Cricket Fans Online</div>
            </div>
            <div class="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
              <div class="flex items-center space-x-2 mb-2">
                <div class="text-orange-400">🏏</div>
                <span class="text-orange-400 font-semibold">Matches</span>
              </div>
              <div class="text-2xl font-bold text-white">{{ liveMatches }}</div>
              <div class="text-gray-400 text-sm">Streaming Live</div>
            </div>
          </div>
        </div>

        <!-- Cricket Links -->
        <div>
          <h3 class="text-xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-red-400">
            🏏 Cricket Hub
          </h3>
          <ul class="space-y-3">
            <li>
              <router-link to="/"
                class="group flex items-center space-x-2 text-gray-300 hover:text-orange-400 transition-colors">
                <span class="group-hover:translate-x-1 transition-transform duration-300">🏠 Home</span>
              </router-link>
            </li>
            <li>
              <router-link to="/live"
                class="group flex items-center space-x-2 text-gray-300 hover:text-orange-400 transition-colors">
                <span class="group-hover:translate-x-1 transition-transform duration-300">📺 Live Cricket</span>
              </router-link>
            </li>
            <li>
              <router-link to="/schedule"
                class="group flex items-center space-x-2 text-gray-300 hover:text-orange-400 transition-colors">
                <span class="group-hover:translate-x-1 transition-transform duration-300">📅 Match Schedule</span>
              </router-link>
            </li>
            <li>
              <a href="#"
                class="group flex items-center space-x-2 text-gray-300 hover:text-orange-400 transition-colors">
                <span class="group-hover:translate-x-1 transition-transform duration-300">🏆 Tournaments</span>
              </a>
            </li>
            <li>
              <a href="#"
                class="group flex items-center space-x-2 text-gray-300 hover:text-orange-400 transition-colors">
                <span class="group-hover:translate-x-1 transition-transform duration-300">📊 Live Scores</span>
              </a>
            </li>
          </ul>
        </div>

        <!-- Support & Help -->
        <div>
          <h3 class="text-xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
            🎧 Support Center
          </h3>
          <ul class="space-y-3">
            <li>
              <a href="#" class="group flex items-center space-x-2 text-gray-300 hover:text-blue-400 transition-colors">
                <span class="group-hover:translate-x-1 transition-transform duration-300">❓ Help Center</span>
              </a>
            </li>
            <li>
              <a href="#" class="group flex items-center space-x-2 text-gray-300 hover:text-blue-400 transition-colors">
                <span class="group-hover:translate-x-1 transition-transform duration-300">📞 Contact Us</span>
              </a>
            </li>
            <li>
              <a href="#" class="group flex items-center space-x-2 text-gray-300 hover:text-blue-400 transition-colors">
                <span class="group-hover:translate-x-1 transition-transform duration-300">🔒 Privacy Policy</span>
              </a>
            </li>
            <li>
              <a href="#" class="group flex items-center space-x-2 text-gray-300 hover:text-blue-400 transition-colors">
                <span class="group-hover:translate-x-1 transition-transform duration-300">📋 Terms of Service</span>
              </a>
            </li>
            <li>
              <a href="#" class="group flex items-center space-x-2 text-gray-300 hover:text-blue-400 transition-colors">
                <span class="group-hover:translate-x-1 transition-transform duration-300">💬 Live Chat</span>
              </a>
            </li>
          </ul>
        </div>
      </div>

      <!-- Enhanced Bottom Bar -->
      <div class="border-t border-gradient-to-r from-orange-500/20 via-red-500/20 to-pink-500/20 mt-12 pt-8">
        <div class="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
          <div class="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6">
            <p class="text-gray-400 text-sm">
              © {{ currentYear }} CricketLive. All rights reserved. Made by S_Bhattarai .
            </p>
          </div>

          <div class="flex items-center space-x-6">
            <!-- Quality Badge -->
            <div
              class="flex items-center space-x-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 px-4 py-2 rounded-full border border-green-500/30">
              <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-green-400 text-sm font-semibold">HD Quality</span>
            </div>

            <!-- 24/7 Badge -->
            <div
              class="flex items-center space-x-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 px-4 py-2 rounded-full border border-blue-500/30">
              <div class="text-blue-400">⚡</div>
              <span class="text-blue-400 text-sm font-semibold">24/7 Streaming</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Tv, Facebook, Twitter, Instagram, Youtube } from 'lucide-vue-next'

const currentYear = computed(() => new Date().getFullYear())
const onlineUsers = ref(1247) // This would come from your backend
const liveMatches = ref(8) // Number of live cricket matches

// Simulate live stats updates
setInterval(() => {
  onlineUsers.value = Math.floor(Math.random() * 2000) + 800
  liveMatches.value = Math.floor(Math.random() * 12) + 3
}, 30000)
</script>
