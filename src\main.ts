import './assets/main.css'
import './utils/console-disabler'
import { consoleDisabler } from './utils/console-disabler'
import './utils/devtools-blocker'
import { devToolsBlocker } from './utils/devtools-blocker'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'

// Security configuration
const DISABLE_CONSOLE = true
const BLOCK_DEVTOOLS = true

if (DISABLE_CONSOLE) consoleDisabler.forceDisable()
if (BLOCK_DEVTOOLS) devToolsBlocker.enable()

const app = createApp(App)

app.use(createPinia())
app.use(router)

app.mount('#app')
