<template>
  <header
    class="bg-gradient-to-r from-slate-900 via-purple-900 to-slate-900 sticky top-0 z-50 border-b border-white/10 shadow-2xl shadow-purple-900/20">
    <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-20">
        <!-- Logo -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center space-x-4 group">
            <div
              class="w-12 h-12 rounded-2xl flex items-center justify-center bg-white/10 shadow-lg group-hover:scale-105 group-hover:shadow-purple-500/40 transition-all duration-300">
              <Tv class="w-7 h-7 text-white" />
            </div>
            <span class="text-3xl font-black text-white drop-shadow-lg">LiveTV</span>
          </router-link>
        </div>

        <!-- Navigation Links -->
        <div class="hidden md:block">
          <div class="ml-10 flex items-center space-x-2">
            <router-link v-for="link in navLinks" :key="link.name" :to="link.path" class="nav-link"
              :class="{ 'nav-link-active': $route.name === link.name }">
              <component :is="link.icon" class="w-5 h-5 mr-2" />
              {{ link.text }}
            </router-link>
          </div>
        </div>

        <!-- Live Indicator -->
        <div class="hidden md:flex items-center">
          <div class="flex items-center space-x-3 px-4 py-2 rounded-full bg-red-900/50 border border-red-500/50">
            <div class="w-2.5 h-2.5 bg-red-500 rounded-full animate-pulse shadow-md shadow-red-500/50"></div>
            <span class="text-red-300 text-sm font-bold tracking-wider">LIVE</span>
          </div>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button @click="toggleMobileMenu" class="mobile-menu-button">
            <Menu v-if="!mobileMenuOpen" class="w-7 h-7" />
            <X v-else class="w-7 h-7" />
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <div v-show="mobileMenuOpen" class="md:hidden">
        <div class="mobile-menu-container">
          <router-link v-for="link in navLinks" :key="link.name" :to="link.path" class="mobile-nav-link"
            @click="closeMobileMenu">
            <component :is="link.icon" class="w-5 h-5 mr-2" />
            {{ link.text }}
          </router-link>
        </div>
      </div>
    </nav>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Tv, Home, Radio, Calendar, Menu, X } from 'lucide-vue-next'

const mobileMenuOpen = ref(false)

const navLinks = [
  { name: 'home', path: '/', text: 'Home', icon: Home },
  { name: 'live', path: '/live', text: 'Live TV', icon: Radio },
  { name: 'schedule', path: '/schedule', text: 'Schedule', icon: Calendar }
]

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

</script>

<style scoped>
.nav-link {
  @apply flex items-center px-4 py-3 rounded-xl text-base font-medium text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-200;
}

.nav-link-active {
  @apply text-white bg-white/10 backdrop-blur-sm;
}

.mobile-menu-button {
  @apply inline-flex items-center justify-center p-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-purple-500 transition-all duration-200;
}

.mobile-menu-container {
  @apply px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-black/90 backdrop-blur-md rounded-xl mt-2 border border-white/10;
}

.mobile-nav-link {
  @apply flex items-center px-4 py-3 rounded-lg text-lg font-medium text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-200;
}
</style>
