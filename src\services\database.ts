import { supabase, type CustomEvent } from '@/lib/supabase'
import type { LiveEvent } from './api'

export class DatabaseService {
  // Initialize database table
  async initializeDatabase() {
    try {
      // Create custom_events table if it doesn't exist
      const { error } = await supabase.rpc('create_custom_events_table')
      if (error && !error.message.includes('already exists')) {
        // Table creation error handled silently
      }
    } catch (error) {
      // Table creation handled by SQL migration
    }
  }

  // Get all custom events
  async getCustomEvents(): Promise<LiveEvent[]> {
    try {
      const { data, error } = await supabase
        .from('custom_events')
        .select('*')
        .order('unix_timestamp', { ascending: false })

      if (error) {
        return []
      }

      // Convert database format to LiveEvent format
      return (data || []).map(event => ({
        match: event.match,
        sport: event.sport,
        tournament: event.tournament,
        unix_timestamp: event.unix_timestamp,
        channels: [event.iframe_url],
        isCustom: true,
        description: event.description,
        id: event.id
      }))
    } catch (error) {
      return []
    }
  }

  // Add custom event
  async addCustomEvent(eventData: {
    match: string
    sport: string
    tournament: string
    date: string
    time: string
    iframeHtml: string
    description?: string
  }): Promise<LiveEvent> {
    try {
      const unix_timestamp = new Date(`${eventData.date} ${eventData.time}`).getTime() / 1000

      const customEvent: Omit<CustomEvent, 'id' | 'created_at' | 'updated_at'> = {
        match: eventData.match,
        sport: eventData.sport,
        tournament: eventData.tournament,
        date: eventData.date,
        time: eventData.time,
        unix_timestamp,
        iframe_url: eventData.iframeHtml, // Store the full HTML
        description: eventData.description || ''
      }

      const { data, error } = await supabase
        .from('custom_events')
        .insert([customEvent])
        .select()
        .single()

      if (error) {
        throw new Error(`Failed to add custom event: ${error.message}`)
      }

      // Convert to LiveEvent format
      return {
        match: data.match,
        sport: data.sport,
        tournament: data.tournament,
        unix_timestamp: data.unix_timestamp,
        channels: [data.iframe_url], // This now contains full HTML
        isCustom: true,
        description: data.description,
        id: data.id
      }
    } catch (error) {
      throw error
    }
  }

  // Remove custom event
  async removeCustomEvent(eventId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('custom_events')
        .delete()
        .eq('id', eventId)

      if (error) {
        throw new Error(`Failed to remove custom event: ${error.message}`)
      }
    } catch (error) {
      throw error
    }
  }

  // Subscribe to real-time changes
  subscribeToCustomEvents(callback: (events: LiveEvent[]) => void) {
    const subscription = supabase
      .channel('custom_events_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'custom_events' },
        async () => {
          // Fetch updated events when changes occur
          const events = await this.getCustomEvents()
          callback(events)
        }
      )
      .subscribe()

    return subscription
  }

  // Unsubscribe from real-time changes
  unsubscribeFromCustomEvents(subscription: any) {
    if (subscription) {
      supabase.removeChannel(subscription)
    }
  }
}

export const databaseService = new DatabaseService()
