// Complete Developer Tools Blocker
// Prevents users from accessing F12, right-click, inspect element, etc.

class DevToolsBlocker {
  private isBlocked = false
  private detectionInterval: any = null

  constructor() {
    this.init()
  }

  // Initialize all blocking mechanisms
  init() {
    this.blockKeyboardShortcuts()
    this.blockRightClick()
    this.blockSelectText()
    this.detectDevTools()
    this.blockDragAndDrop()
    this.blockPrintScreen()
    this.addWarningMessage()
  }

  // Block all keyboard shortcuts for developer tools
  blockKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Block F12
      if (e.key === 'F12') {
        e.preventDefault()
        e.stopPropagation()
        this.showWarning('Developer tools are disabled')
        return false
      }

      // Block Ctrl+Shift+I (Inspect Element)
      if (e.ctrlKey && e.shiftKey && e.key === 'I') {
        e.preventDefault()
        e.stopPropagation()
        this.showWarning('Inspect element is disabled')
        return false
      }

      // Block Ctrl+Shift+J (Console)
      if (e.ctrlKey && e.shiftKey && e.key === 'J') {
        e.preventDefault()
        e.stopPropagation()
        this.showWarning('Console is disabled')
        return false
      }

      // Block Ctrl+Shift+C (Element selector)
      if (e.ctrlKey && e.shiftKey && e.key === 'C') {
        e.preventDefault()
        e.stopPropagation()
        this.showWarning('Element selector is disabled')
        return false
      }

      // Block Ctrl+U (View Source)
      if (e.ctrlKey && e.key === 'u') {
        e.preventDefault()
        e.stopPropagation()
        this.showWarning('View source is disabled')
        return false
      }

      // Block Ctrl+S (Save Page)
      if (e.ctrlKey && e.key === 's') {
        e.preventDefault()
        e.stopPropagation()
        this.showWarning('Save page is disabled')
        return false
      }

      // Block Ctrl+A (Select All)
      if (e.ctrlKey && e.key === 'a') {
        e.preventDefault()
        e.stopPropagation()
        return false
      }

      // Block Ctrl+P (Print)
      if (e.ctrlKey && e.key === 'p') {
        e.preventDefault()
        e.stopPropagation()
        this.showWarning('Printing is disabled')
        return false
      }

      // Block Ctrl+Shift+K (Firefox Console)
      if (e.ctrlKey && e.shiftKey && e.key === 'K') {
        e.preventDefault()
        e.stopPropagation()
        return false
      }

      // Block F5 and Ctrl+R (Refresh) - Optional
      // if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
      //   e.preventDefault()
      //   return false
      // }
    })
  }

  // Block right-click context menu
  blockRightClick() {
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault()
      e.stopPropagation()
      this.showWarning('Right-click is disabled')
      return false
    })
  }

  // Block text selection
  blockSelectText() {
    document.addEventListener('selectstart', (e) => {
      e.preventDefault()
      return false
    })

    document.addEventListener('dragstart', (e) => {
      e.preventDefault()
      return false
    })

    // CSS to prevent text selection
    const style = document.createElement('style')
    style.textContent = `
      * {
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
        -webkit-touch-callout: none !important;
        -webkit-tap-highlight-color: transparent !important;
      }
      
      input, textarea {
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
        user-select: text !important;
      }
    `
    document.head.appendChild(style)
  }

  // Detect if developer tools are open
  detectDevTools() {
    let devtools = { open: false, orientation: null }
    
    setInterval(() => {
      if (window.outerHeight - window.innerHeight > 200 || 
          window.outerWidth - window.innerWidth > 200) {
        if (!devtools.open) {
          devtools.open = true
          this.handleDevToolsOpen()
        }
      } else {
        devtools.open = false
      }
    }, 500)

    // Alternative detection method
    let element = new Image()
    Object.defineProperty(element, 'id', {
      get: () => {
        this.handleDevToolsOpen()
        return 'devtools-detector'
      }
    })

    // DevTools detection active
  }

  // Handle when developer tools are detected
  handleDevToolsOpen() {
    // Redirect to warning page or close tab
    document.body.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #1f2937;
        color: white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-family: Arial, sans-serif;
        z-index: 999999;
      ">
        <div style="text-align: center; max-width: 600px; padding: 40px;">
          <h1 style="font-size: 3rem; margin-bottom: 20px;">🔒</h1>
          <h2 style="font-size: 2rem; margin-bottom: 20px; color: #ef4444;">Access Denied</h2>
          <p style="font-size: 1.2rem; margin-bottom: 30px; line-height: 1.6;">
            Developer tools are not allowed on this website for security reasons.
          </p>
          <p style="font-size: 1rem; color: #9ca3af; margin-bottom: 30px;">
            Please close developer tools and refresh the page to continue.
          </p>
          <button onclick="window.location.reload()" style="
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 1rem;
            cursor: pointer;
          ">
            Refresh Page
          </button>
        </div>
      </div>
    `
  }

  // Block drag and drop
  blockDragAndDrop() {
    document.addEventListener('drop', (e) => {
      e.preventDefault()
      return false
    })

    document.addEventListener('dragover', (e) => {
      e.preventDefault()
      return false
    })
  }

  // Block print screen (limited effectiveness)
  blockPrintScreen() {
    document.addEventListener('keyup', (e) => {
      if (e.key === 'PrintScreen') {
        navigator.clipboard.writeText('')
        this.showWarning('Screenshot is disabled')
      }
    })
  }

  // Add warning message to console (if somehow accessed)
  addWarningMessage() {
    // Warning message disabled for cleaner production
  }

  // Show warning message
  showWarning(message: string) {
    // Create warning toast
    const warning = document.createElement('div')
    warning.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #ef4444;
      color: white;
      padding: 15px 20px;
      border-radius: 6px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 999999;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      animation: slideIn 0.3s ease-out;
    `
    warning.textContent = message

    // Add animation
    const style = document.createElement('style')
    style.textContent = `
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `
    document.head.appendChild(style)

    document.body.appendChild(warning)

    // Remove after 3 seconds
    setTimeout(() => {
      if (warning.parentNode) {
        warning.parentNode.removeChild(warning)
      }
    }, 3000)
  }

  // Enable blocking
  enable() {
    if (!this.isBlocked) {
      this.init()
      this.isBlocked = true
    }
  }

  // Disable blocking (for development)
  disable() {
    this.isBlocked = false
    // Note: Some blocks like event listeners can't be easily removed
    // Recommend page refresh after disabling
  }
}

// Create singleton instance
export const devToolsBlocker = new DevToolsBlocker()

// Auto-enable in production
if (import.meta.env.PROD || import.meta.env.MODE === 'production') {
  devToolsBlocker.enable()
}

export default devToolsBlocker
