<template>
  <div class="min-h-screen bg-black text-white">
    <!-- Hero Header -->
    <section
      class="relative py-8 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 border-b border-white/10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <!-- Header Content -->
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-3">
              <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <h1
                class="text-3xl md:text-4xl font-black bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent">
                LIVE TV
              </h1>
            </div>
            <div class="hidden md:flex items-center space-x-6 text-sm">
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                <span class="text-red-400 font-medium">{{ filteredLiveEvents.length }} LIVE NOW</span>
              </div>
              <div class="flex items-center space-x-2">
                <Tv class="w-4 h-4 text-purple-400" />
                <span class="text-gray-300">{{ filteredEvents.length }} Total Events</span>
              </div>
            </div>
          </div>

          <!-- Controls -->
          <div class="flex flex-col sm:flex-row gap-4">
            <!-- Search -->
            <div class="relative">
              <Search class="absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input v-model="searchQuery" type="text" placeholder="Search events..."
                class="pl-12 pr-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 transition-all duration-300 w-64">
            </div>

            <!-- Sport Filter -->
            <select v-model="selectedSport"
              class="px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white focus:outline-none focus:border-purple-500/50 transition-all duration-300">
              <option value="" class="bg-slate-800 text-white">All Sports</option>
              <option v-for="sport in allSports" :key="sport" :value="sport" class="bg-slate-800 text-white">
                {{ apiService.getSportIcon(sport) }} {{ sport }}
              </option>
            </select>

            <!-- Refresh Button -->
            <button @click="refreshEvents" :disabled="loading"
              class="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-bold rounded-xl hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-purple-500/50 disabled:opacity-50 disabled:cursor-not-allowed">
              <RefreshCw :class="['w-4 h-4 mr-2 inline', { 'animate-spin': loading }]" />
              Refresh
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 xl:grid-cols-4 gap-8">
        <!-- Events Sidebar -->
        <div class="xl:col-span-1 order-2 xl:order-1">
          <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden">
            <!-- Sidebar Header -->
            <div class="p-6 border-b border-white/10">
              <h2 class="text-xl font-bold text-white mb-2">Live Events</h2>
              <p class="text-gray-400 text-sm">{{ displayedEventsCount }} event{{ displayedEventsCount !== 1 ? 's' : ''
                }} available</p>
            </div>

            <!-- Events List -->
            <div class="max-h-[calc(100vh-300px)] overflow-y-auto">
              <!-- Loading State -->
              <div v-if="loading" class="p-6 space-y-4">
                <div v-for="i in 5" :key="i" class="animate-pulse">
                  <div class="h-4 bg-white/10 rounded w-3/4 mb-2"></div>
                  <div class="h-3 bg-white/10 rounded w-1/2"></div>
                </div>
              </div>

              <!-- Empty State -->
              <div v-else-if="filteredEvents.length === 0" class="p-8 text-center">
                <Tv class="w-16 h-16 mx-auto mb-4 text-gray-400" />
                <h3 class="text-lg font-bold text-white mb-2">No events found</h3>
                <p class="text-gray-400 text-sm">Try adjusting your search or filters</p>
              </div>

              <!-- Events -->
              <div v-else class="p-4 space-y-4">
                <!-- Admin Featured Events -->
                <div v-if="customEventsForDisplay.length > 0" class="mb-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-bold text-green-400 flex items-center">
                      <Crown class="w-4 h-4 mr-2" />
                      Admin Featured
                    </h3>
                    <span class="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full">
                      {{ customEventsForDisplay.length }}
                    </span>
                  </div>

                  <div class="space-y-3">
                    <div v-for="event in customEventsForDisplay" :key="event.id"
                      class="group relative bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-sm rounded-xl p-4 border border-green-500/30 hover:border-green-500/60 transition-all duration-300 cursor-pointer hover:scale-105"
                      @click="selectEvent(event)">

                      <!-- Admin Delete Button -->
                      <button v-if="isAdmin" @click.stop="deleteCustomEvent(event.id!)"
                        class="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-all duration-300 bg-red-500 hover:bg-red-600 text-white rounded-full p-2 shadow-lg z-10 hover:scale-110">
                        <Trash class="w-3 h-3" />
                      </button>

                      <div class="flex items-start space-x-3 pr-8">
                        <div class="w-3 h-3 bg-green-500 rounded-full mt-1 flex-shrink-0"></div>
                        <div class="flex-1 min-w-0">
                          <div class="flex items-center space-x-2 mb-2">
                            <span
                              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-bold bg-green-500 text-white">
                              <Crown class="w-3 h-3 mr-1" />
                              ADMIN
                            </span>
                            <span class="text-xs text-green-300">{{ apiService.getEventTime(event.unix_timestamp)
                              }}</span>
                          </div>
                          <h4 class="font-bold text-white text-sm mb-1 line-clamp-2">{{ event.match }}</h4>
                          <p class="text-xs text-green-300 mb-2">{{ event.tournament }}</p>
                          <div class="flex items-center space-x-2 text-xs text-green-400">
                            <Tv class="w-3 h-3" />
                            <span>Direct Stream</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Regular Events -->
                <div v-if="regularEventsForDisplay.length > 0">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-semibold text-gray-300">
                      {{ customEventsForDisplay.length > 0 ? 'More Events' : 'All Events' }}
                    </h3>
                    <span class="text-xs text-gray-500">{{ regularEventsForDisplay.length }}</span>
                  </div>

                  <div class="space-y-3">
                    <div v-for="event in regularEventsForDisplay" :key="event.unix_timestamp"
                      @click="selectEvent(event)" :class="[
                        'group relative bg-white/5 backdrop-blur-sm rounded-xl p-4 border transition-all duration-300 cursor-pointer hover:scale-105',
                        selectedEvent?.unix_timestamp === event.unix_timestamp
                          ? 'border-purple-500/50 bg-purple-500/10'
                          : 'border-white/10 hover:border-purple-500/30 hover:bg-white/10'
                      ]">

                      <div class="flex items-start space-x-3">
                        <div class="w-3 h-3 rounded-full mt-1 flex-shrink-0" :class="{
                          'bg-red-500 animate-pulse': apiService.isEventLive(event.unix_timestamp),
                          'bg-blue-500': !apiService.isEventLive(event.unix_timestamp)
                        }">
                        </div>
                        <div class="flex-1 min-w-0">
                          <div class="flex items-center space-x-2 mb-2">
                            <span v-if="apiService.isEventLive(event.unix_timestamp)"
                              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-500/20 text-red-300 border border-red-500/30">
                              <div class="w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse"></div>
                              LIVE
                            </span>
                            <span v-else
                              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30">
                              <div class="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
                              UPCOMING
                            </span>
                            <span class="text-xs text-gray-400">{{ apiService.getEventTime(event.unix_timestamp)
                              }}</span>
                          </div>
                          <h4 class="font-medium text-white text-sm mb-1 line-clamp-2">{{ event.match }}</h4>
                          <p class="text-xs text-gray-400 mb-2">{{ event.tournament }} • {{ event.sport }}</p>
                          <div class="flex items-center space-x-2 text-xs text-purple-400">
                            <Tv class="w-3 h-3" />
                            <span>{{ event.channels.length }} channel{{ event.channels.length !== 1 ? 's' : '' }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Video Player -->
        <div ref="iframeContainer" class="xl:col-span-3 order-1 xl:order-2">
          <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden">
            <!-- No Event Selected -->
            <div v-if="!selectedEvent"
              class="aspect-video bg-gradient-to-br from-slate-900 to-purple-900 flex items-center justify-center relative overflow-hidden">
              <!-- Background Elements -->
              <div class="absolute inset-0">
                <div class="absolute top-10 left-10 w-32 h-32 bg-purple-500/20 rounded-full blur-2xl animate-pulse">
                </div>
                <div
                  class="absolute bottom-10 right-10 w-40 h-40 bg-pink-500/20 rounded-full blur-2xl animate-pulse delay-1000">
                </div>
              </div>

              <div class="relative z-10 text-center px-4">
                <div
                  class="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center">
                  <Tv class="w-12 h-12 text-white" />
                </div>
                <h3 class="text-2xl font-bold text-white mb-4">Select a Live Event</h3>
                <p class="text-gray-300 max-w-md mx-auto">
                  Choose from the list of live events to start streaming. Admin featured events are highlighted in
                  green.
                </p>
              </div>
            </div>

            <!-- Event Playing -->
            <div v-else>
              <!-- Video Player -->
              <div class="aspect-video bg-black relative overflow-hidden">
                <!-- Custom Event with Full Iframe HTML -->
                <div v-if="selectedChannel && selectedEvent?.isCustom" v-html="selectedChannel"
                  class="w-full h-full iframe-container">
                </div>

                <!-- Regular Event with URL -->
                <iframe v-else-if="selectedChannel" :src="selectedChannel"
                  allow="autoplay; fullscreen; encrypted-media; accelerometer; gyroscope; picture-in-picture"
                  allowfullscreen width="100%" height="100%" scrolling="no" frameborder="0"
                  referrerpolicy="no-referrer-when-downgrade" class="w-full h-full"
                  style="border: none; outline: none;">
                </iframe>

                <!-- No Channel Selected -->
                <div v-else class="w-full h-full flex items-center justify-center text-white">
                  <div class="text-center">
                    <AlertCircle class="w-16 h-16 mx-auto mb-4 text-yellow-400" />
                    <h3 class="text-xl font-bold mb-2">No Channel Selected</h3>
                    <p class="text-gray-300">Please select a channel to start streaming</p>
                  </div>
                </div>

                <!-- Live Overlay -->
                <!-- <div v-if="selectedEvent" class="absolute top-4 left-4">
                  <div
                    class="flex items-center space-x-2 bg-black/50 backdrop-blur-sm px-3 py-2 rounded-full border border-white/20">
                    <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                    <span class="text-white text-sm font-medium">
                      {{ selectedEvent.isCustom ? 'ADMIN STREAM' : 'LIVE NOW' }}
                    </span>
                  </div>
                </div> -->
              </div>

              <!-- Player Info -->
              <div class="p-6">
                <!-- Event Details -->
                <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4 mb-6">
                  <div class="flex-1">
                    <h2 class="text-2xl font-bold text-white mb-2">{{ selectedEvent.match }}</h2>
                    <p class="text-lg text-gray-300 mb-2">{{ selectedEvent.tournament }} • {{ selectedEvent.sport }}</p>
                    <p v-if="selectedEvent.description" class="text-gray-400 leading-relaxed">
                      {{ selectedEvent.description }}
                    </p>
                  </div>

                  <div class="flex items-center space-x-3">
                    <span v-if="selectedEvent.isCustom"
                      class="inline-flex items-center px-4 py-2 rounded-full text-sm font-bold bg-green-500/20 text-green-400 border border-green-500/30">
                      <Crown class="w-4 h-4 mr-2" />
                      ADMIN FEATURED
                    </span>
                    <span v-else-if="apiService.isEventLive(selectedEvent.unix_timestamp)"
                      class="inline-flex items-center px-4 py-2 rounded-full text-sm font-bold bg-red-500/20 text-red-400 border border-red-500/30">
                      <div class="w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></div>
                      LIVE NOW
                    </span>
                    <span v-else
                      class="inline-flex items-center px-4 py-2 rounded-full text-sm font-bold bg-blue-500/20 text-blue-400 border border-blue-500/30">
                      <Clock class="w-4 h-4 mr-2" />
                      UPCOMING
                    </span>
                  </div>
                </div>

                <!-- Channel Selection -->
                <div v-if="selectedEvent.channels.length > 1">
                  <h3 class="text-lg font-bold text-white mb-4">Available Channels:</h3>
                  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    <button v-for="(channel, index) in selectedEvent.channels" :key="index"
                      @click="selectChannel(channel)" :class="[
                        'p-4 rounded-xl border transition-all duration-300 text-left hover:scale-105',
                        selectedChannel === channel
                          ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white border-purple-500 shadow-lg'
                          : 'bg-white/5 text-gray-300 border-white/20 hover:border-purple-500/50 hover:bg-white/10'
                      ]">
                      <div class="font-bold mb-1">{{ apiService.getChannelName(channel) }}</div>
                      <div class="text-sm opacity-75">{{ apiService.getChannelRegion(channel) }}</div>
                    </button>
                  </div>
                </div>

                <!-- Single Channel Info -->
                <div v-else-if="selectedEvent.channels.length === 1"
                  class="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                      <div class="font-bold text-white">{{ apiService.getChannelName(selectedEvent.channels[0]) }}</div>
                      <div class="text-sm text-gray-400">{{ apiService.getChannelRegion(selectedEvent.channels[0]) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Live Users -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex items-center space-x-2 text-sm text-gray-300">
        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        <span>{{ liveUsers }} Live Users</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useLiveTvStore } from '@/stores/counter'
import { apiService } from '@/services/api'
import { authService } from '@/services/auth'
import {
  Tv,
  Play,
  RefreshCw,
  AlertCircle,
  Search,
  Trash,
  Crown,
  Info,
  Clock
} from 'lucide-vue-next'

const store = useLiveTvStore()
const searchQuery = ref('')
const selectedSport = ref('')

const loading = computed(() => store.loading)
const todayEvents = computed(() => store.todayEvents)
const liveEvents = computed(() => store.liveEvents)
const totalEvents = computed(() => store.totalEvents)
const allSports = computed(() => store.allSports)
const selectedEvent = computed(() => store.selectedEvent)
const selectedChannel = computed(() => store.selectedChannel)

const iframeContainer = ref<HTMLElement | null>(null)

const liveUsers = ref(1)
const channel = new BroadcastChannel('liveUsers')

// Get all events from all dates (including custom events)
const allAvailableEvents = computed(() => {
  const allEvents: any[] = []

  // Get all events from all dates
  Object.values(store.allEvents).forEach(dayEvents => {
    allEvents.push(...dayEvents)
  })

  return allEvents
})

const filteredEvents = computed(() => {
  let events = allAvailableEvents.value

  // Filter by sport
  if (selectedSport.value) {
    events = events.filter(event => event.sport === selectedSport.value)
  }

  // Filter by search query
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    events = events.filter(event =>
      event.match.toLowerCase().includes(query) ||
      event.sport.toLowerCase().includes(query) ||
      event.tournament.toLowerCase().includes(query) ||
      (event.description && event.description.toLowerCase().includes(query))
    )
  }

  // Sort by timestamp (most recent first)
  return events.sort((a, b) => b.unix_timestamp - a.unix_timestamp)
})

// Count live events in filtered results
const filteredLiveEvents = computed(() => {
  return filteredEvents.value.filter(event =>
    event.isCustom || apiService.isEventLive(event.unix_timestamp)
  )
})

// Separate custom and regular events for special display
const customEventsForDisplay = computed(() => {
  return filteredEvents.value.filter(event => event.isCustom)
})

const regularEventsForDisplay = computed(() => {
  return filteredEvents.value.filter(event =>
    !event.isCustom && apiService.isEventLive(event.unix_timestamp)
  )
})

const displayedEventsCount = computed(() => {
  return customEventsForDisplay.value.length + regularEventsForDisplay.value.length
})

// Check if user is admin
const isAdmin = computed(() => {
  return authService.isAuthenticated()
})

const selectEvent = (event: any) => {
  store.selectEvent(event)
  if (iframeContainer.value) {
    iframeContainer.value.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

const selectChannel = (channel: string) => {
  store.selectChannel(channel)
}

const refreshEvents = async () => {
  await store.fetchEvents()
}

const deleteCustomEvent = async (eventId: string) => {
  if (confirm('Are you sure you want to delete this custom event?')) {
    try {
      await store.removeCustomEvent(eventId)
      // If this was the selected event, clear selection
      if (selectedEvent.value && (selectedEvent.value as any).id === eventId) {
        store.clearSelection()
      }
    } catch (error) {
      alert('Error deleting event. Please try again.')
    }
  }
}

onMounted(async () => {
  await store.fetchEvents()
  await store.fetchChannels()
  await store.loadCustomEvents()
  store.subscribeToCustomEvents()

  channel.postMessage({ action: 'join' })

  channel.onmessage = (event) => {
    if (event.data.action === 'join') {
      liveUsers.value++
    } else if (event.data.action === 'leave') {
      liveUsers.value--
    }
  }
})

onUnmounted(() => {
  store.unsubscribeFromCustomEvents()
  channel.postMessage({ action: 'leave' })
  channel.close()
})
</script>

<style scoped>
/* Iframe container for custom events */
.iframe-container {
  position: relative;
  overflow: hidden;
}

.iframe-container :deep(iframe) {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.group {
  animation: slideIn 0.6s ease-out;
}

/* Custom scrollbar */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
