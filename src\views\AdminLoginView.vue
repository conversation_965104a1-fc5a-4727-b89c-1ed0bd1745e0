<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div class="max-w-md w-full">
      <!-- Logo/Brand -->
      <div class="text-center mb-8">
        <div class="mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
          <Settings class="h-8 w-8 text-white" />
        </div>
        <h1 class="text-3xl font-bold text-gray-900">LiveTV Admin</h1>
        <p class="text-gray-600 mt-2">Secure admin access</p>
      </div>

      <!-- Login Form -->
      <div class="bg-white rounded-xl shadow-lg p-8">
        <form @submit.prevent="handleLogin" class="space-y-6">
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
              Admin Password
            </label>
            <input id="password" v-model="password" type="password" required placeholder="Enter admin password"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 text-lg"
              style="background-color: white !important; color: #1f2937 !important;" :disabled="loading" />
          </div>

          <button type="submit" :disabled="loading || !password.trim()"
            class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium text-lg">
            <span v-if="loading" class="flex items-center justify-center">
              <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              Logging in...
            </span>
            <span v-else>Access Admin Panel</span>
          </button>
        </form>

        <!-- Error Message -->
        <div v-if="error" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-red-700 text-sm">{{ error }}</p>
        </div>

        <!-- Info -->
        <div class="mt-6 text-center">
          <p class="text-xs text-gray-500">
            Authorized personnel only. All access is logged.
          </p>
        </div>
      </div>

      <!-- Back to Site -->
      <div class="text-center mt-6">
        <router-link to="/" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
          ← Back to LiveTV
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Settings } from 'lucide-vue-next'
import { authService } from '@/services/auth'

const router = useRouter()
const password = ref('')
const loading = ref(false)
const error = ref('')

const handleLogin = async () => {
  loading.value = true
  error.value = ''

  try {
    const result = await authService.login(password.value)

    if (result.success) {
      // Redirect to admin panel
      router.push('/admin')
    } else {
      error.value = result.message
      password.value = ''
    }
  } catch (err) {
    error.value = 'Login failed. Please try again.'
    password.value = ''
  } finally {
    loading.value = false
  }
}

// Check if already authenticated
onMounted(() => {
  if (authService.isAuthenticated()) {
    router.push('/admin')
  }

  // Start session monitoring
  authService.startSessionMonitoring()
})
</script>

<style scoped>
/* Additional styling for the login page */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

input:focus {
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}
</style>
