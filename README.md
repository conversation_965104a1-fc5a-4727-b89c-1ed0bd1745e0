# LiveTV - Vue.js Live Sports Streaming Platform

A modern, interactive Vue.js application for live sports streaming with iframe embedding using topembed.pw channels. Features a beautiful landing page, live TV section, schedule management, and admin panel.

## 🚀 Features

### 🏠 **Interactive Landing Page**
- Hero section with gradient backgrounds and call-to-action buttons
- Real-time statistics (live events, total channels, active users)
- Feature showcase with responsive design
- Smooth animations and modern UI

### 📺 **Live TV Section**
- Dynamic event listing with live indicators
- iframe embedding for topembed.pw channels
- Multiple channel selection for each event
- Channel information with region display
- Real-time event updates

### 📅 **Schedule Management**
- Date-wise event organization
- Sport filtering with dynamic icons
- Live event indicators
- Time until event countdown
- Quick watch and reminder buttons

### ⚙️ **Admin Panel**
- Dashboard with comprehensive statistics
- Event and channel management
- System settings (auto-refresh, maintenance mode)
- Recent activity monitoring
- Export functionality

## 🛠 **Technology Stack**

- **Vue 3** with Composition API
- **TypeScript** for type safety
- **Vite** for fast development
- **Pinia** for state management
- **Vue Router** for navigation
- **Tailwind CSS** for styling
- **Lucide Vue** for icons
- **Axios** for API calls

## 📡 **API Integration**

The application integrates with `https://topembed.pw/api.php` which returns events in this format:

```json
{
  "events": {
    "2025-06-20": [
      {
        "unix_timestamp": 1750372800,
        "sport": "Baseball",
        "tournament": "MLB",
        "match": "Miami Marlins - Philadelphia Phillies",
        "channels": [
          "https://topembed.pw/channel/FanDuelSportsFlorida[USA]",
          "https://topembed.pw/channel/NBCSPhiladelphia[USA]"
        ]
      }
    ]
  }
}
```

## 🎯 **iframe Implementation**

Channels are embedded using the exact format required by topembed.pw:

```html
<iframe
  allow="encrypted-media"
  width="100%"
  height="100%"
  scrolling="no"
  frameborder="0"
  allowfullscreen
  src="https://topembed.pw/channel/TNTSports1[UK]"
></iframe>
```

## 🚀 **Quick Start**

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

### Available Scripts

```bash
# Development
npm run dev          # Start dev server at http://localhost:5173

# Production
npm run build        # Build for production
npm run preview      # Preview production build

# Code Quality
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
npm run type-check   # TypeScript type checking

# Testing
npm run test:unit    # Run unit tests
```

## 📁 **Project Structure**

```
src/
├── components/
│   ├── Layout/
│   │   ├── AppHeader.vue    # Navigation header
│   │   └── AppFooter.vue    # Footer component
│   └── __tests__/           # Component tests
├── views/
│   ├── HomeView.vue         # Landing page
│   ├── LiveView.vue         # Live TV streaming
│   ├── ScheduleView.vue     # Event schedule
│   └── AdminView.vue        # Admin panel
├── stores/
│   └── counter.ts           # Pinia store for state management
├── services/
│   └── api.ts               # API service with utilities
├── router/
│   └── index.ts             # Vue Router configuration
└── assets/
    └── main.css             # Global styles with Tailwind
```

## 🎨 **Key Features Implemented**

### Dynamic API Integration
- Real-time data fetching from topembed.pw API
- Fallback mock data for development
- Error handling and loading states
- Automatic data refresh

### Enhanced Channel Management
- Channel name extraction from URLs
- Region detection (USA, UK, etc.)
- Multiple channel support per event
- Channel testing capabilities

### Smart Event Handling
- Live event detection
- Time until event calculations
- Sport categorization with icons
- Date-wise organization

### Responsive Design
- Mobile-first approach
- Tailwind CSS for consistent styling
- Interactive animations
- Glass effects and gradients

## 🔧 **Configuration**

### API Endpoint
Update the API URL in `src/services/api.ts`:
```typescript
private baseURL = 'https://topembed.pw/api.php'
```

### Styling
Customize colors and themes in `tailwind.config.js`

## 🚀 **Deployment**

### Build for Production
```bash
npm run build
```

### Deploy to Netlify/Vercel
The `dist` folder contains the production build ready for deployment.

---

**Built with ❤️ using Vue.js and modern web technologies**
