import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface CustomEvent {
  id?: string
  match: string
  sport: string
  tournament: string
  date: string
  time: string
  unix_timestamp: number
  iframe_url: string
  description?: string
  created_at?: string
  updated_at?: string
}

// Admin authentication
export const adminAuth = {
  // Simple admin check - you can enhance this later
  isAdmin: () => {
    return localStorage.getItem('admin_authenticated') === 'true'
  },
  
  login: (password: string) => {
    // Simple password check - you can enhance this later
    const adminPassword = 'admin123' // Change this to your preferred password
    if (password === adminPassword) {
      localStorage.setItem('admin_authenticated', 'true')
      return true
    }
    return false
  },
  
  logout: () => {
    localStorage.removeItem('admin_authenticated')
  }
}
